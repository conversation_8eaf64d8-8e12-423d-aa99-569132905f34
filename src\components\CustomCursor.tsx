import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useCursor } from '../hooks/useCursor';

const CustomCursor: React.FC = () => {
  const { cursorPosition, isHovering } = useCursor();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleMouseEnter = () => setIsVisible(true);
    const handleMouseLeave = () => setIsVisible(false);

    document.addEventListener('mouseenter', handleMouseEnter);
    document.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      document.removeEventListener('mouseenter', handleMouseEnter);
      document.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  if (!isVisible) return null;

  return (
    <>
      {/* Cursor dot */}
      <motion.div
        className="cursor-dot"
        animate={{
          x: cursorPosition.x - 4,
          y: cursorPosition.y - 4,
          scale: isHovering ? 1.5 : 1,
        }}
        transition={{
          type: 'spring',
          stiffness: 500,
          damping: 28,
        }}
      />
      
      {/* Cursor outline */}
      <motion.div
        className="cursor-outline"
        animate={{
          x: cursorPosition.x - 16,
          y: cursorPosition.y - 16,
          scale: isHovering ? 1.5 : 1,
        }}
        transition={{
          type: 'spring',
          stiffness: 150,
          damping: 15,
        }}
      />
    </>
  );
};

export default CustomCursor;
