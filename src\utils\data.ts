import { Project, Skill, Experience, SocialLink } from '../types';

export const projects: Project[] = [
  {
    id: '1',
    title: 'E-Commerce Platform',
    description: 'Modern e-commerce platform with React, Node.js, and Stripe integration',
    longDescription: 'A full-stack e-commerce solution featuring user authentication, product management, shopping cart functionality, and secure payment processing. Built with modern technologies and optimized for performance.',
    image: '/api/placeholder/600/400',
    technologies: ['React', 'TypeScript', 'Node.js', 'MongoDB', 'Stripe', 'TailwindCSS'],
    githubUrl: 'https://github.com/username/ecommerce-platform',
    liveUrl: 'https://ecommerce-demo.vercel.app',
    featured: true,
    category: 'web',
  },
  {
    id: '2',
    title: 'AI Chat Application',
    description: 'Real-time chat application with AI-powered responses and sentiment analysis',
    longDescription: 'An intelligent chat application that integrates OpenAI GPT for automated responses, real-time messaging with Socket.io, and sentiment analysis for enhanced user experience.',
    image: '/api/placeholder/600/400',
    technologies: ['React', 'Socket.io', 'OpenAI API', 'Express', 'PostgreSQL', 'Redis'],
    githubUrl: 'https://github.com/username/ai-chat-app',
    liveUrl: 'https://ai-chat-demo.vercel.app',
    featured: true,
    category: 'ai',
  },
  {
    id: '3',
    title: 'Task Management Dashboard',
    description: 'Collaborative project management tool with real-time updates',
    longDescription: 'A comprehensive project management dashboard featuring task tracking, team collaboration, real-time updates, and advanced analytics. Designed for modern development teams.',
    image: '/api/placeholder/600/400',
    technologies: ['Next.js', 'Prisma', 'PostgreSQL', 'NextAuth', 'Framer Motion'],
    githubUrl: 'https://github.com/username/task-dashboard',
    liveUrl: 'https://task-dashboard-demo.vercel.app',
    featured: false,
    category: 'web',
  },
];

export const skills: Skill[] = [
  { id: '1', name: 'React', icon: '⚛️', level: 95, category: 'frontend', color: '#61DAFB' },
  { id: '2', name: 'TypeScript', icon: '📘', level: 90, category: 'frontend', color: '#3178C6' },
  { id: '3', name: 'Next.js', icon: '▲', level: 88, category: 'frontend', color: '#000000' },
  { id: '4', name: 'Node.js', icon: '🟢', level: 85, category: 'backend', color: '#339933' },
  { id: '5', name: 'Python', icon: '🐍', level: 82, category: 'backend', color: '#3776AB' },
  { id: '6', name: 'PostgreSQL', icon: '🐘', level: 80, category: 'backend', color: '#336791' },
  { id: '7', name: 'AWS', icon: '☁️', level: 75, category: 'devops', color: '#FF9900' },
  { id: '8', name: 'Docker', icon: '🐳', level: 78, category: 'devops', color: '#2496ED' },
  { id: '9', name: 'Figma', icon: '🎨', level: 85, category: 'design', color: '#F24E1E' },
  { id: '10', name: 'Three.js', icon: '🎮', level: 70, category: 'frontend', color: '#000000' },
];

export const experiences: Experience[] = [
  {
    id: '1',
    company: 'Tech Innovations Inc.',
    position: 'Senior Frontend Developer',
    duration: '2022 - Present',
    description: 'Leading frontend development for enterprise applications, mentoring junior developers, and implementing modern React architectures.',
    technologies: ['React', 'TypeScript', 'Next.js', 'GraphQL', 'AWS'],
  },
  {
    id: '2',
    company: 'Digital Solutions Ltd.',
    position: 'Full Stack Developer',
    duration: '2020 - 2022',
    description: 'Developed and maintained multiple client projects, from concept to deployment, using modern web technologies.',
    technologies: ['React', 'Node.js', 'MongoDB', 'Express', 'Docker'],
  },
  {
    id: '3',
    company: 'StartupXYZ',
    position: 'Frontend Developer',
    duration: '2019 - 2020',
    description: 'Built responsive web applications and collaborated with design teams to create exceptional user experiences.',
    technologies: ['React', 'JavaScript', 'SCSS', 'REST APIs', 'Git'],
  },
];

export const socialLinks: SocialLink[] = [
  {
    id: '1',
    name: 'GitHub',
    url: 'https://github.com/username',
    icon: 'Github',
    color: '#333',
  },
  {
    id: '2',
    name: 'LinkedIn',
    url: 'https://linkedin.com/in/username',
    icon: 'Linkedin',
    color: '#0077B5',
  },
  {
    id: '3',
    name: 'Twitter',
    url: 'https://twitter.com/username',
    icon: 'Twitter',
    color: '#1DA1F2',
  },
  {
    id: '4',
    name: 'Email',
    url: 'mailto:<EMAIL>',
    icon: 'Mail',
    color: '#EA4335',
  },
];

export const personalInfo = {
  name: 'Alex Developer',
  title: 'Full Stack Developer',
  subtitle: 'Building digital experiences that matter',
  bio: 'Passionate full-stack developer with 5+ years of experience creating modern web applications. I specialize in React, TypeScript, and Node.js, with a keen eye for design and user experience.',
  location: 'San Francisco, CA',
  email: '<EMAIL>',
  phone: '+****************',
  resumeUrl: '/resume.pdf',
};
