import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { fadeInUp, staggerContainer, scaleIn } from '../utils/animations';
import { useScrollAnimation } from '../hooks/useScrollAnimation';
import { skills } from '../utils/data';
import { Skill } from '../types';

const Skills: React.FC = () => {
  const { ref, inView } = useScrollAnimation(0.2);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [hoveredSkill, setHoveredSkill] = useState<string | null>(null);

  const categories = [
    { id: 'all', name: 'All Skills', color: '#0ea5e9' },
    { id: 'frontend', name: 'Frontend', color: '#10b981' },
    { id: 'backend', name: 'Backend', color: '#f59e0b' },
    { id: 'mobile', name: 'Mobile', color: '#8b5cf6' },
    { id: 'devops', name: 'DevOps', color: '#ef4444' },
    { id: 'design', name: 'Design', color: '#ec4899' },
  ];

  const filteredSkills = selectedCategory === 'all' 
    ? skills 
    : skills.filter(skill => skill.category === selectedCategory);

  const SkillCard: React.FC<{ skill: Skill; index: number }> = ({ skill, index }) => (
    <motion.div
      variants={scaleIn}
      transition={{ delay: index * 0.1 }}
      className="group relative"
      onMouseEnter={() => setHoveredSkill(skill.id)}
      onMouseLeave={() => setHoveredSkill(null)}
    >
      <div className="glass rounded-xl p-6 h-full transition-all duration-300 group-hover:glow-border">
        {/* Skill Icon */}
        <div className="relative mb-4">
          <motion.div
            className="w-16 h-16 mx-auto rounded-full flex items-center justify-center text-3xl"
            style={{ backgroundColor: `${skill.color}20`, border: `2px solid ${skill.color}40` }}
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ type: 'spring', stiffness: 300 }}
          >
            {skill.icon}
          </motion.div>
          
          {/* Glow effect */}
          <motion.div
            className="absolute inset-0 rounded-full blur-xl opacity-0 group-hover:opacity-30 transition-opacity duration-300"
            style={{ backgroundColor: skill.color }}
          />
        </div>

        {/* Skill Name */}
        <h3 className="text-lg font-semibold text-white text-center mb-3">
          {skill.name}
        </h3>

        {/* Skill Level */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">Proficiency</span>
            <span className="text-primary-400 font-medium">{skill.level}%</span>
          </div>
          
          {/* Progress Bar */}
          <div className="w-full bg-dark-700 rounded-full h-2 overflow-hidden">
            <motion.div
              className="h-full rounded-full"
              style={{ backgroundColor: skill.color }}
              initial={{ width: 0 }}
              animate={inView ? { width: `${skill.level}%` } : { width: 0 }}
              transition={{ duration: 1, delay: index * 0.1 }}
            />
          </div>
        </div>

        {/* Hover Effect */}
        <AnimatePresence>
          {hoveredSkill === skill.id && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="absolute inset-0 rounded-xl flex items-center justify-center"
              style={{ backgroundColor: `${skill.color}10` }}
            >
              <motion.div
                initial={{ y: 20 }}
                animate={{ y: 0 }}
                className="text-center"
              >
                <div className="text-4xl mb-2">{skill.icon}</div>
                <div className="text-white font-semibold">{skill.name}</div>
                <div className="text-sm text-gray-300 capitalize">{skill.category}</div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );

  return (
    <section id="skills" className="py-20 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 cyber-grid opacity-5" />
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-accent-500/10 rounded-full blur-3xl" />
      <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl" />

      <div className="container mx-auto px-6" ref={ref}>
        {/* Section Header */}
        <motion.div
          variants={staggerContainer}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mb-16"
        >
          <motion.h2
            variants={fadeInUp}
            className="text-4xl md:text-5xl font-bold mb-4 glow-text"
          >
            Skills & Technologies
          </motion.h2>
          <motion.p
            variants={fadeInUp}
            className="text-xl text-gray-400 max-w-2xl mx-auto"
          >
            A comprehensive toolkit for building modern digital experiences
          </motion.p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          variants={staggerContainer}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category, index) => (
            <motion.button
              key={category.id}
              variants={fadeInUp}
              transition={{ delay: index * 0.1 }}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                selectedCategory === category.id
                  ? 'text-white shadow-lg'
                  : 'text-gray-400 hover:text-white'
              }`}
              style={{
                backgroundColor: selectedCategory === category.id ? category.color : 'transparent',
                border: `2px solid ${category.color}`,
                boxShadow: selectedCategory === category.id ? `0 0 20px ${category.color}40` : 'none',
              }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {category.name}
            </motion.button>
          ))}
        </motion.div>

        {/* Skills Grid */}
        <AnimatePresence mode="wait">
          <motion.div
            key={selectedCategory}
            variants={staggerContainer}
            initial="hidden"
            animate="visible"
            exit="hidden"
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            {filteredSkills.map((skill, index) => (
              <SkillCard key={skill.id} skill={skill} index={index} />
            ))}
          </motion.div>
        </AnimatePresence>

        {/* Skills Summary */}
        <motion.div
          variants={staggerContainer}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="mt-20 grid md:grid-cols-3 gap-8"
        >
          <motion.div
            variants={fadeInUp}
            className="text-center glass rounded-xl p-6"
          >
            <div className="text-3xl font-bold text-primary-400 mb-2">
              {skills.filter(s => s.level >= 90).length}
            </div>
            <div className="text-gray-400">Expert Level</div>
          </motion.div>

          <motion.div
            variants={fadeInUp}
            className="text-center glass rounded-xl p-6"
          >
            <div className="text-3xl font-bold text-accent-400 mb-2">
              {skills.filter(s => s.level >= 80 && s.level < 90).length}
            </div>
            <div className="text-gray-400">Advanced Level</div>
          </motion.div>

          <motion.div
            variants={fadeInUp}
            className="text-center glass rounded-xl p-6"
          >
            <div className="text-3xl font-bold text-green-400 mb-2">
              {skills.filter(s => s.level < 80).length}
            </div>
            <div className="text-gray-400">Learning</div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Skills;
