import React, { useRef, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Text, Box, Sphere } from '@react-three/drei';
import { Mesh, Vector3 } from 'three';
import * as THREE from 'three';

// Floating Laptop Component
const FloatingLaptop: React.FC = () => {
  const laptopRef = useRef<Mesh>(null);
  
  useFrame((state) => {
    if (laptopRef.current) {
      laptopRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
      laptopRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.8) * 0.2;
    }
  });

  return (
    <group ref={laptopRef}>
      {/* Laptop Base */}
      <Box args={[3, 0.1, 2]} position={[0, -0.5, 0]}>
        <meshStandardMaterial color="#1e293b" />
      </Box>
      
      {/* Laptop Screen */}
      <Box args={[2.8, 1.8, 0.05]} position={[0, 0.4, -0.9]} rotation={[-0.1, 0, 0]}>
        <meshStandardMaterial color="#0f172a" />
      </Box>
      
      {/* Screen Content */}
      <Box args={[2.6, 1.6, 0.01]} position={[0, 0.4, -0.87]} rotation={[-0.1, 0, 0]}>
        <meshStandardMaterial color="#0ea5e9" emissive="#0ea5e9" emissiveIntensity={0.2} />
      </Box>
      
      {/* Code Lines */}
      {[...Array(8)].map((_, i) => (
        <Box
          key={i}
          args={[1.8 - i * 0.1, 0.05, 0.01]}
          position={[-0.3, 0.6 - i * 0.15, -0.86]}
          rotation={[-0.1, 0, 0]}
        >
          <meshStandardMaterial 
            color={i % 3 === 0 ? "#10b981" : i % 3 === 1 ? "#f59e0b" : "#8b5cf6"} 
            emissive={i % 3 === 0 ? "#10b981" : i % 3 === 1 ? "#f59e0b" : "#8b5cf6"}
            emissiveIntensity={0.1}
          />
        </Box>
      ))}
    </group>
  );
};

// Tech Stack Cube Component
const TechCube: React.FC = () => {
  const cubeRef = useRef<Mesh>(null);
  
  const techLogos = [
    { name: 'React', color: '#61DAFB', position: new Vector3(0, 0, 1.1) },
    { name: 'TS', color: '#3178C6', position: new Vector3(0, 0, -1.1) },
    { name: 'Node', color: '#339933', position: new Vector3(1.1, 0, 0) },
    { name: 'Next', color: '#000000', position: new Vector3(-1.1, 0, 0) },
    { name: 'AWS', color: '#FF9900', position: new Vector3(0, 1.1, 0) },
    { name: 'DB', color: '#336791', position: new Vector3(0, -1.1, 0) },
  ];

  useFrame((state) => {
    if (cubeRef.current) {
      cubeRef.current.rotation.x = state.clock.elapsedTime * 0.2;
      cubeRef.current.rotation.y = state.clock.elapsedTime * 0.3;
    }
  });

  return (
    <group ref={cubeRef}>
      {/* Main Cube */}
      <Box args={[2, 2, 2]}>
        <meshStandardMaterial 
          color="#1e293b" 
          transparent 
          opacity={0.8}
          wireframe={false}
        />
      </Box>
      
      {/* Wireframe Overlay */}
      <Box args={[2.1, 2.1, 2.1]}>
        <meshStandardMaterial 
          color="#0ea5e9" 
          wireframe 
          transparent 
          opacity={0.3}
        />
      </Box>
      
      {/* Tech Logos */}
      {techLogos.map((tech, index) => (
        <group key={index} position={tech.position}>
          <Text
            fontSize={0.3}
            color={tech.color}
            anchorX="center"
            anchorY="middle"
            font="/fonts/JetBrainsMono-Bold.woff"
          >
            {tech.name}
          </Text>
          <Sphere args={[0.05]} position={[0, -0.3, 0]}>
            <meshStandardMaterial color={tech.color} emissive={tech.color} emissiveIntensity={0.2} />
          </Sphere>
        </group>
      ))}
    </group>
  );
};

// Particle System
const ParticleField: React.FC = () => {
  const particlesRef = useRef<THREE.Points>(null);
  
  const particles = useMemo(() => {
    const positions = new Float32Array(100 * 3);
    for (let i = 0; i < 100; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 20;
      positions[i * 3 + 1] = (Math.random() - 0.5) * 20;
      positions[i * 3 + 2] = (Math.random() - 0.5) * 20;
    }
    return positions;
  }, []);

  useFrame((state) => {
    if (particlesRef.current) {
      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.05;
    }
  });

  return (
    <points ref={particlesRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={100}
          array={particles}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial size={0.05} color="#0ea5e9" transparent opacity={0.6} />
    </points>
  );
};

interface ModelViewerProps {
  type?: 'laptop' | 'cube';
  className?: string;
}

const ModelViewer: React.FC<ModelViewerProps> = ({ type = 'laptop', className = '' }) => {
  return (
    <div className={`w-full h-full ${className}`}>
      <Canvas
        camera={{ position: [0, 0, 8], fov: 50 }}
        style={{ background: 'transparent' }}
      >
        {/* Lighting */}
        <ambientLight intensity={0.4} />
        <pointLight position={[10, 10, 10]} intensity={1} color="#0ea5e9" />
        <pointLight position={[-10, -10, -10]} intensity={0.5} color="#d946ef" />
        <spotLight
          position={[0, 10, 0]}
          angle={0.3}
          penumbra={1}
          intensity={0.8}
          color="#ffffff"
        />

        {/* 3D Models */}
        {type === 'laptop' ? <FloatingLaptop /> : <TechCube />}
        
        {/* Particle Field */}
        <ParticleField />

        {/* Controls */}
        <OrbitControls
          enableZoom={false}
          enablePan={false}
          autoRotate
          autoRotateSpeed={0.5}
          maxPolarAngle={Math.PI / 2}
          minPolarAngle={Math.PI / 2}
        />
      </Canvas>
    </div>
  );
};

export default ModelViewer;
